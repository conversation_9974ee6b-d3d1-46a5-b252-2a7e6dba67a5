
import base64
import os
from google import genai
from google.genai import types
from bson import ObjectId
import json
from datetime import datetime, timezone
import asyncio

# Direct imports - these should not cause circular imports
from app.shared.db_enums import TaskStatus, QuizType, InputType, GenerationType
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
logger = setup_new_logging(__name__)

async def followup_generate(task_set_id: str, current_user: UserTenantDB) -> None:
    """
    Generate followup tasks based on completed task set (primary or followup).
    Supports multi-level followup generation up to configured maximum depth.

    Args:
        task_set_id: The completed task set ID (primary or followup)
        current_user: The current user object from Redis
    """
    try:
        # Import media config to get followup generator count
        from .helpers.media_config_loader import get_media_config

        # Get media configuration to check followup generator count
        config = get_media_config(current_user=current_user)
        max_followup_count = config.get_followup_generator_count()
        logger.info(f"Followup generation config: max_followup_count = {max_followup_count}")

        # Get the completed task set and its tasks
        task_set = await current_user.async_db.task_sets.find_one(
            {"_id": ObjectId(task_set_id)},
            {
                "tasks": 1, "session_id": 1, "title": 1, "difficulty_level": 1,
                "input_content": 1, "gentype": 1, "followup_count": 1,
                "original_task_set_id": 1, "parent_task_set_id": 1
            }
        )

        if not task_set:
            logger.error(f"Task set {task_set_id} not found for followup generation")
            return

        # Get current followup count (0 for primary, 1+ for followups)
        current_followup_count = task_set.get("followup_count", 0)
        gentype = task_set.get("gentype", "unknown")
        logger.info(f"🔄 FOLLOWUP GENERATION: Task set {task_set_id} | gentype: {gentype} | current_followup_count: {current_followup_count} | max_followup_count: {max_followup_count}")

        # Check if we've reached the maximum followup depth
        if current_followup_count >= max_followup_count:
            logger.info(f"🛑 FOLLOWUP GENERATION: Task set {task_set_id} has reached maximum followup depth ({current_followup_count}/{max_followup_count}), skipping followup generation")
            return

        # Determine the original task set ID for reference
        original_task_set_id = task_set.get("original_task_set_id")
        if not original_task_set_id:
            # This is a primary task set, use its own ID as the original
            if task_set.get("gentype") == "primary":
                original_task_set_id = ObjectId(task_set_id)
            else:
                logger.error(f"Task set {task_set_id} is not primary but has no original_task_set_id")
                return

        # Get the original task set's input content for audio data
        original_task_set = None
        if str(original_task_set_id) != task_set_id:
            # This is a followup, get the original task set
            original_task_set = await current_user.async_db.task_sets.find_one(
                {"_id": original_task_set_id},
                {"input_content": 1}
            )
            if not original_task_set:
                logger.error(f"Original task set {original_task_set_id} not found for followup generation")
                return
        else:
            # This is the primary task set
            original_task_set = task_set

        # Get all task items from original task set and all previous followup levels
        all_task_sets_to_include = []

        # Always include the original task set
        original_task_set_obj = await current_user.async_db.task_sets.find_one(
            {"_id": original_task_set_id},
            {"tasks": 1, "followup_count": 1}
        )
        if original_task_set_obj:
            all_task_sets_to_include.append({
                "tasks": original_task_set_obj.get("tasks", []),
                "level": "Original"
            })

        # Include all previous followup levels (1 to current_followup_count)
        for level in range(1, current_followup_count + 1):
            followup_task_set = await current_user.async_db.task_sets.find_one(
                {
                    "original_task_set_id": original_task_set_id,
                    "followup_count": level
                },
                {"tasks": 1}
            )
            if followup_task_set:
                all_task_sets_to_include.append({
                    "tasks": followup_task_set.get("tasks", []),
                    "level": f"Level {level}"
                })

        # Collect all task IDs from all levels
        all_task_ids = []
        for task_set_info in all_task_sets_to_include:
            all_task_ids.extend([ObjectId(tid) for tid in task_set_info["tasks"]])

        # Get all task items from all levels
        task_items = await current_user.async_db.task_items.find(
            {"_id": {"$in": all_task_ids}},
            {"question": 1, "correct_answer": 1, "_id": 1}
        ).to_list(length=None)

        # Create a mapping of task_id to task_item for organizing by level
        task_items_map = {str(task["_id"]): task for task in task_items}





        # Build previous tasks string organized by level for context
        prev_task = ""
        for task_set_info in all_task_sets_to_include:
            prev_task += f"\n=== {task_set_info['level']} Questions ===\n"

            for task_id in task_set_info["tasks"]:
                task = task_items_map.get(task_id)
                if task:
                    question = task.get("question", {})
                    correct_answer = task.get("correct_answer", {})

                    # Clean up question data
                    question_copy = question.copy()
                    question_copy.pop("media_url", None)
                    question_copy.pop("metadata", None)

                    prev_task += f"Question: {question_copy}\nAnswer: {correct_answer}\n\n"

        logger.info(f"🔄 Building followup level {current_followup_count + 1} using original audio + questions from {len(all_task_sets_to_include)} previous levels")
        # Get audio data from the original task set
        audio_data = await asyncio.to_thread(
            current_user.minio.get_audio_bytes,
            original_task_set.get("input_content", {}).get("object_name", "")
        )
        # Generate followup tasks using the existing generate function
        result = generate(audio_data, prev_task)

        if result and "tasks" in result:
            # Save the generated followup tasks
            task_item_ids = await save_followup_tasks(
                result["tasks"],
                task_set_id,
                current_user,
                task_set.get("session_id", ""),
                task_set.get("title", "Followup Tasks"),
                task_set.get("difficulty_level", 1),
                current_user.async_db,
                current_followup_count + 1,  # Next level
                original_task_set_id  # Always reference original
            )

            # Generate media for the followup tasks (options audio, task images, task audio)
            if task_item_ids:
                await generate_followup_media(current_user, task_item_ids)

            logger.info(f"Successfully generated and saved {len(result['tasks'])} followup tasks with media for task set {task_set_id}")
        else:
            logger.error(f"Failed to generate followup tasks for task set {task_set_id}")

    except Exception as e:
        logger.error(f"Error in followup_generate for task set {task_set_id}: {e}")
        raise

async def generate_followup_media(current_user: UserTenantDB, task_item_ids: list):
    """
    Generate media for followup tasks including options audio, task images, and task audio.

    Args:
        current_user: The current user object
        task_item_ids: List of task item ObjectIds that need media generation
    """
    try:
        logger.info(f"🎨 FOLLOWUP MEDIA: Starting media generation for {len(task_item_ids)} followup tasks")

        # Import media generation functions
        from .helpers.options_audio_generator import generate_options_audio
        from .helpers.task_media_generator import generate_task_media_independent

        # Process each task for media generation
        for i, task_id in enumerate(task_item_ids, 1):
            try:
                # Get the task item from database
                task_item = await current_user.async_db.task_items.find_one(
                    {"_id": task_id},
                    {"type": 1, "question": 1, "title": 1}
                )

                if not task_item:
                    logger.error(f"❌ FOLLOWUP MEDIA: Task {task_id} not found")
                    continue

                logger.info(f"🎨 FOLLOWUP MEDIA: Processing task {i}/{len(task_item_ids)} | ID: {task_id} | Type: {task_item.get('type')}")

                # Generate task-specific media (images/audio) first
                await generate_task_media_independent(current_user, task_item)

                # Generate options audio for all tasks with options
                if task_item.get("question", {}).get("options"):
                    await generate_options_audio(current_user, task_item, task_id)

                # Add delay between tasks to prevent rate limiting
                if i < len(task_item_ids):  # Don't delay after the last task
                    await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"❌ FOLLOWUP MEDIA: Error generating media for task {task_id}: {e}")
                continue

        logger.info(f"✅ FOLLOWUP MEDIA: Completed media generation for {len(task_item_ids)} followup tasks")

    except Exception as e:
        logger.error(f"❌ FOLLOWUP MEDIA: Error in generate_followup_media: {e}")
        # Don't raise - media generation failure shouldn't fail the entire followup process

async def save_followup_tasks(tasks, parent_task_set_id: str, current_user: UserTenantDB, session_id: str, title: str, difficulty_level: int, tenant_db, followup_count: int, original_task_set_id: ObjectId):
    """
    Save generated followup tasks to the database.

    Args:
        tasks: List of generated task data
        parent_task_set_id: The parent task set ID that was completed
        current_user: The current user object from Redis
        session_id: The session ID
        title: The title for the followup task set
        difficulty_level: The difficulty level
        tenant_db: The tenant database connection
        followup_count: The followup level/depth (1, 2, 3, etc.)
        original_task_set_id: The original primary task set ID for reference
    """
    try:
        # Create followup task set
        followup_task_set_id = ObjectId()
        followup_task_set_doc = {
            "_id": followup_task_set_id,
            "user_id": ObjectId(current_user.user.id),
            "session_id": session_id,
            "title": f"Followup Level {followup_count}: {title}",
            "input_type": InputType.AUDIO,
            "tasks": [],  # Will be populated with task IDs
            "total_tasks": len(tasks),
            "attempted_tasks": 0,
            "status": TaskStatus.PENDING,
            "gentype": GenerationType.FOLLOW_UP.value,
            "is_followup": True,
            "parent_task_set_id": ObjectId(parent_task_set_id),
            "followup_count": followup_count,
            "original_task_set_id": original_task_set_id,
            "total_score": 0,
            "scored": 0,
            "attempts_count": 0,
            "difficulty_level": difficulty_level,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }

        # Create task items
        task_items = []
        task_ids = []
        total_score = 0

        for i, task_data in enumerate(tasks):
            task_item_id = ObjectId()
            task_ids.append(str(task_item_id))  # Store as string for consistency with primary task sets

            # Map task type
            task_type = task_data.get("type", "single_choice")
            if task_type == "single_choice":
                quiz_type = QuizType.SINGLE_CHOICE
                score = 10
            elif task_type == "multiple_choice":
                quiz_type = QuizType.MULTIPLE_CHOICE
                score = 10
            elif task_type == "image_identification":
                quiz_type = QuizType.IMAGE_IDENTIFICATION
                score = 10
            elif task_type == "speak_word":
                quiz_type = QuizType.SPEAK_WORD
                score = 15
            else:
                quiz_type = QuizType.SINGLE_CHOICE
                score = 10

            total_score += score

            # Create task item document
            task_item = {
                "_id": task_item_id,
                "type": quiz_type.value,
                "title": task_data.get("title", f"Followup Task {i+1}"),
                "question": {
                    "type": quiz_type.value,
                    "text": task_data.get("text", ""),
                    "translated_text": task_data.get("translated_text", ""),
                    "options": task_data.get("options", {}),
                    "answer_hint": task_data.get("answer_hint", "")
                },
                "correct_answer": {
                    "type": quiz_type.value,
                    "value": task_data.get("answer", "")
                },
                "status": TaskStatus.PENDING.value,
                "total_score": score,
                "scored": 0,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "difficulty_level": difficulty_level,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
            task_items.append(task_item)

        # Update task set with task IDs and total score
        followup_task_set_doc["tasks"] = task_ids
        followup_task_set_doc["total_score"] = total_score

        # Insert task set and task items
        await tenant_db.task_sets.insert_one(followup_task_set_doc)
        if task_items:
            await tenant_db.task_items.insert_many(task_items)

        logger.info(f"Saved followup task set {followup_task_set_id} with {len(task_items)} tasks")

    except Exception as e:
        logger.error(f"Error saving followup tasks: {e}")
        raise

def generate(audio_data, previous_tasks):
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    prompt = """
`
You are a Nepali language tutor designing interactive quiz tasks for **{age}-year-old children**.
### Instructions:
You are given a **Nepali audio recording**.
Your task is to generate exactly **{num_tasks} quiz questions** based **only on what is clearly said in the audio**.
Do **not** add fictional details or imagined content.  
Only include things **explicitly present in the audio**.  
Do **not repeat or recreate** any of these previous tasks:

{previous_tasks}

### Question Format:

Each task must follow this question-type cycle:

1. "single\_choice"
2. "multiple\_choice"
3. "image\_identification"
4. "speak\_word"

(Repeat this order if `num_tasks` > 4)

### Quiz Question Structure:

```json
{{
  "title": "Quiz Task Title",
  "type": "single_choice" | "multiple_choice" | "image_identification" | "speak_word",
  "text": "Question in Nepali",
  "translated_text": "English translation",
  "options": {{
    "a": "घोड़ा",
    "b": "कुकुर",
    "c": "हात्ती"
  }}, 
  "answer_hint": "elephant", 
  "answer": "a" 
}}
```

### Guidelines:

* Use simple, vivid Nepali suited for children.
* All content must be directly grounded in the audio.
* For "speak\_word":

  * Only one Nepali word (no phrases or sentences).
* For "image\_identification":

  * Ask general visual questions, e.g., “What is seen in the picture?”
  * Do not name the object directly in the question.
* Use single-word answer options (no phrases).
* "answer\_hint" must be:

  * In English for "single\_choice", "multiple\_choice", and "image\_identification".
  * In Nepali (one word only) for "speak\_word".
""".format(
        age=8,  # Example age, can be parameterized
        num_tasks=5,  # Example number of tasks, can be parameterized
        previous_tasks=previous_tasks
    )

    model = "gemini-2.0-flash"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type="audio/mp4",
                    data=audio_data
                ),
            ],
        ),
    ]
    generate_content_config = types.GenerateContentConfig(
        temperature=0,
        response_mime_type="application/json",
        system_instruction=[
            types.Part.from_text(text=prompt),
        ],
    )

    # Generate content stream
    output=""
    
    for chunk in client.models.generate_content_stream(
        model=model,
        contents=contents,
        config=generate_content_config,
    ):
        output += chunk.text


    return {
         "tasks":json.loads(output),
         "usage": {},
    }

if __name__ == "__main__":

    audio_file_path = "/home/<USER>/Documents/nextai/nepali_app/ttsmaker-file-2025-6-7-15-3-11.mp3"
    with open(audio_file_path, "rb") as audio_file:
        audio_data = audio_file.read()

    previous_tasks="""
'Question: {\'text\': "अडियोमा कति पटक \'हेलो\' शब्द प्रयोग भएको छ?", \'translated_text\': "How many times is the word \'Hello\' used in the audio?", \'options\': {\'a\': \'दुई\', \'b\': \'तीन\', \'c\': \'चार\', \'d\': \'पाँच\'}, \'answer_hint\': \'हेलो\'}\nAnswer: {\'value\': \'b\', \'type\': \'single\'}\n\nQuestion: {\'text\': \'अडियोमा कसले बोलिरहेको छ?\', \'translated_text\': \'Who is speaking in the audio?\', \'options\': {\'a\': \'शिक्षक\', \'b\': \'विद्यार्थी\', \'c\': \'एक व्यक्ति\', \'d\': \'दुई व्यक्ति\'}, \'answer_hint\': \'व्यक्ति\'}\nAnswer: {\'value\': \'d\', \'type\': \'single\'}\n\nQuestion: {\'text\': \'यो तस्वीरमा के देखिन्छ?\', \'translated_text\': \'What is seen in this picture?\', \'options\': {\'a\': \'किताब\', \'b\': \'घर\', \'c\': \'फोन\', \'d\': \'कम्प्युटर\'}, \'answer_hint\': "I will generate an image of a phone. The image will depict a modern smartphone with a sleek design, resting on a textured wooden surface. The phone\'s screen will be illuminated, displaying a vibrant and colorful abstract wallpaper. The camera lens on the back will be subtly visible, and the overall lighting will create soft highlights and shadows, emphasizing the phone\'s form and materials.\\n\\n"}\nAnswer: {\'value\': \'c\', \'type\': \'multiple\'}\n\n'
"""
    result =    generate(audio_data, previous_tasks)
    print("Generated content:")
    print(json.dumps(result, indent=2, ensure_ascii=False))